# syntax=docker/dockerfile:1
# Development Dockerfile for AI Orchestrator
# Optimized for hot-reloading and debugging with BuildKit cache mounting

FROM python:3.11.7-slim-bullseye

# Prevent debconf warnings during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies for development
RUN apt-get update && apt-get install -y \
  curl \
  ca-certificates \
  git \
  build-essential \
  && apt-get upgrade -y \
  && rm -rf /var/lib/apt/lists/*

# Set the working directory
WORKDIR /app

# Create a non-root user for development
RUN useradd -m appuser

# Copy requirements and install dependencies with CPU-only PyTorch to avoid CUDA downloads
COPY requirements-base.txt .
COPY requirements.txt .

# Install CPU-only PyTorch first to avoid CUDA dependencies
RUN --mount=type=cache,target=/root/.cache/pip \
  pip install --root-user-action=ignore \
  torch==2.1.2+cpu \
  torchvision==0.16.2+cpu \
  torchaudio==2.1.2+cpu \
  --index-url https://download.pytorch.org/whl/cpu

# Install PyTorch-dependent packages that need CPU-only versions
RUN --mount=type=cache,target=/root/.cache/pip \
  pip install --root-user-action=ignore \
  sentence-transformers>=2.2.2 \
  transformers>=4.35.0

# Install remaining base dependencies
RUN --mount=type=cache,target=/root/.cache/pip \
  pip install --root-user-action=ignore -r requirements-base.txt && \
  pip install --root-user-action=ignore debugpy watchdog

# Copy the application code
COPY src ./src

# Change ownership to non-root user
RUN chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Ensure Python packages are in PATH for appuser
ENV PATH="/home/<USER>/.local/bin:$PATH"

# Expose both application and debug ports
EXPOSE 8000 5678

# Add development-specific health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD python -c "import urllib.request; urllib.request.urlopen('http://localhost:8000/api/v1/health', timeout=5)" || exit 1

# Default command (will be overridden by docker-compose)
CMD ["echo", "Please use docker-compose to run this container"]