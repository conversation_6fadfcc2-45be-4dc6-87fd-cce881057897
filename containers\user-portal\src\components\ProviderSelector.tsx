/**
 * ProviderSelector Component
 * Dropdown component to select an LLM provider with proper accessibility
 */

import React from 'react';
import Select from 'react-select';
import { ChevronDownIcon } from '@heroicons/react/24/outline';
import clsx from 'clsx';
import { LLMProvider } from '@/types/role';
import type { ProviderSelectorProps, ProviderOption } from '@/types/role';

// Mock data for providers
const MOCK_PROVIDERS: ProviderOption[] = [
  {
    value: LLMProvider.OLLAMA,
    label: 'Ollama',
    requiresApiKey: false,
    description: 'Local LLM serving with Ollama',
  },
  {
    value: LLMProvider.OPENROUTER,
    label: 'OpenRouter',
    requiresApiKey: true,
    description: 'Access multiple AI models through OpenRouter',
  },
  {
    value: LLMProvider.OPENAI,
    label: 'OpenAI',
    requiresApiKey: true,
    description: 'GPT-4, GPT-3.5-Turbo, and other OpenAI models',
  },
  {
    value: LLMProvider.ANTHROPIC,
    label: 'Anthropic',
    requiresApiKey: true,
    description: 'Claude 3 Sonnet, Haiku, and Opus models',
  },
];

const ProviderSelector: React.FC<ProviderSelectorProps> = ({
  value,
  onChange,
  providers = MOCK_PROVIDERS,
  disabled = false,
  error,
}) => {
  const selectedProvider = providers.find(p => p.value === value);

  const customStyles = {
    control: (provided: any, state: any) => ({
      ...provided,
      minHeight: '42px',
      borderColor: error
        ? '#ef4444'
        : state.isFocused
        ? '#3b82f6'
        : '#d1d5db',
      borderWidth: '1px',
      borderRadius: '8px',
      boxShadow: state.isFocused
        ? error
          ? '0 0 0 3px rgba(239, 68, 68, 0.1)'
          : '0 0 0 3px rgba(59, 130, 246, 0.1)'
        : 'none',
      '&:hover': {
        borderColor: error ? '#ef4444' : '#9ca3af',
      },
    }),
    option: (provided: any, state: any) => ({
      ...provided,
      backgroundColor: state.isSelected
        ? '#3b82f6'
        : state.isFocused
        ? '#f3f4f6'
        : 'white',
      color: state.isSelected ? 'white' : '#374151',
      padding: '12px 16px',
      '&:hover': {
        backgroundColor: state.isSelected ? '#3b82f6' : '#f3f4f6',
      },
    }),
    placeholder: (provided: any) => ({
      ...provided,
      color: '#9ca3af',
    }),
    singleValue: (provided: any) => ({
      ...provided,
      color: '#374151',
    }),
  };

  const formatOptionLabel = (option: ProviderOption) => (
    <div className="flex items-center justify-between">
      <div>
        <div className="font-medium text-gray-900">{option.label}</div>
        <div className="text-sm text-gray-500">{option.description}</div>
      </div>
      {option.requiresApiKey && (
        <span className="ml-2 rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
          API Key Required
        </span>
      )}
    </div>
  );

  return (
    <div className="space-y-2">
      <label
        htmlFor="provider-select"
        className="block text-sm font-medium text-gray-700"
      >
        LLM Provider
        <span className="text-red-500 ml-1">*</span>
      </label>

      <Select
        instanceId="provider-select"
        inputId="provider-select"
        value={selectedProvider}
        onChange={(option) => onChange(option?.value as LLMProvider)}
        options={providers}
        formatOptionLabel={formatOptionLabel}
        styles={customStyles}
        isDisabled={disabled}
        placeholder="Select an LLM provider..."
        className={clsx('react-select-container', {
          'opacity-50 cursor-not-allowed': disabled,
        })}
        classNamePrefix="react-select"
        aria-label="Select LLM Provider"
        aria-describedby={error ? 'provider-error' : undefined}
      />

      {error && (
        <p
          id="provider-error"
          className="text-sm text-red-600"
          role="alert"
          aria-live="polite"
        >
          {error}
        </p>
      )}

      {selectedProvider && !error && (
        <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
          <div className="font-medium mb-1">Selected Provider: {selectedProvider.label}</div>
          <div>{selectedProvider.description}</div>
          {selectedProvider.requiresApiKey && (
            <div className="mt-2 text-amber-700 bg-amber-50 p-2 rounded border-l-4 border-amber-400">
              <strong>Note:</strong> This provider requires an API key for authentication.
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ProviderSelector;