/**
 * ConfigManager Component
 * Top-level component that orchestrates role-based LLM configuration management
 */

import React, { useState, useEffect } from 'react';
import {
  UserGroupIcon,
  PlusIcon,
  TrashIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';
import clsx from 'clsx';
import RoleConfigPanel from './RoleConfigPanel';
import { LLMProvider } from '@/types/role';
import type {
  ConfigManagerProps,
  RoleConfiguration,
  RoleConfigurationUpdate,
  ApiError,
} from '@/types/role';

// Mock data for initial state
const MOCK_ROLES: Record<string, RoleConfiguration> = {
  architect: {
    provider: LLMProvider.OPENROUTER,
    available_models: ['anthropic/claude-3-sonnet', 'openai/gpt-4'],
    selected_model: 'anthropic/claude-3-sonnet',
    api_key: 'sk-or-v1-...',
    cost_limit: 100.0,
    max_tokens: 4096,
    temperature: 0.7,
    enabled: true,
    created_at: '2024-01-01T00:00:00.000000',
    updated_at: '2024-01-01T00:00:00.000000',
  },
  backend: {
    provider: LLMProvider.OLLAMA,
    available_models: ['codellama:13b', 'deepseek-coder:6.7b'],
    selected_model: 'codellama:13b',
    api_key: null,
    cost_limit: null,
    max_tokens: 4096,
    temperature: 0.3,
    enabled: true,
    created_at: '2024-01-01T00:00:00.000000',
    updated_at: '2024-01-01T00:00:00.000000',
  },
  frontend: {
    provider: LLMProvider.OPENAI,
    available_models: ['gpt-4o', 'gpt-3.5-turbo'],
    selected_model: 'gpt-4o',
    api_key: 'sk-...',
    cost_limit: 75.0,
    max_tokens: 2048,
    temperature: 0.5,
    enabled: true,
    created_at: '2024-01-01T00:00:00.000000',
    updated_at: '2024-01-01T00:00:00.000000',
  },
  issue_fix: {
    provider: LLMProvider.ANTHROPIC,
    available_models: ['claude-3-sonnet', 'claude-3-haiku'],
    selected_model: 'claude-3-sonnet',
    api_key: 'sk-ant-...',
    cost_limit: 50.0,
    max_tokens: 4096,
    temperature: 0.1,
    enabled: true,
    created_at: '2024-01-01T00:00:00.000000',
    updated_at: '2024-01-01T00:00:00.000000',
  },
  shell: {
    provider: LLMProvider.OLLAMA,
    available_models: ['llama2:13b', 'codellama:7b'],
    selected_model: 'llama2:13b',
    api_key: null,
    cost_limit: null,
    max_tokens: 2048,
    temperature: 0.2,
    enabled: true,
    created_at: '2024-01-01T00:00:00.000000',
    updated_at: '2024-01-01T00:00:00.000000',
  },
};

const ConfigManager: React.FC<ConfigManagerProps> = ({ className }) => {
  const [roles, setRoles] = useState<Record<string, RoleConfiguration>>(MOCK_ROLES);
  const [activeRole, setActiveRole] = useState<string>('architect');
  const [isLoading, setIsLoading] = useState<Record<string, boolean>>({});
  const [errors, setErrors] = useState<Record<string, ApiError | null>>({});
  const [showDeleteModal, setShowDeleteModal] = useState<string | null>(null);
  const [newRoleName, setNewRoleName] = useState('');
  const [showCreateForm, setShowCreateForm] = useState(false);

  // Simulate API loading on mount
  useEffect(() => {
    setIsLoading(prev => ({ ...prev, global: true }));
    const timer = setTimeout(() => {
      setIsLoading(prev => ({ ...prev, global: false }));
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Handle role configuration changes
  const handleConfigurationChange = (roleName: string, updates: RoleConfigurationUpdate) => {
    setRoles(prevRoles => ({
      ...prevRoles,
      [roleName]: {
        ...prevRoles[roleName],
        ...updates,
        updated_at: new Date().toISOString(),
      },
    }));

    // Clear any previous errors for this role
    setErrors(prev => ({ ...prev, [roleName]: null }));
  };

  // Handle saving configuration
  const handleSave = async (roleName: string) => {
    setIsLoading(prev => ({ ...prev, [roleName]: true }));
    setErrors(prev => ({ ...prev, [roleName]: null }));

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Simulate random errors for demonstration
      if (Math.random() < 0.1) {
        throw new Error('Simulated API error');
      }

      // Update the role's updated timestamp
      setRoles(prevRoles => ({
        ...prevRoles,
        [roleName]: {
          ...prevRoles[roleName],
          updated_at: new Date().toISOString(),
        },
      }));

    } catch (error) {
      setErrors(prev => ({
        ...prev,
        [roleName]: {
          message: 'Failed to save configuration',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      }));
    } finally {
      setIsLoading(prev => ({ ...prev, [roleName]: false }));
    }
  };

  // Handle role creation
  const handleCreateRole = async () => {
    if (!newRoleName.trim()) return;

    const roleName = newRoleName.trim().toLowerCase().replace(/\s+/g, '_');

    if (roles[roleName]) {
      setErrors(prev => ({
        ...prev,
        create: { message: 'Role name already exists' },
      }));
      return;
    }

    setIsLoading(prev => ({ ...prev, create: true }));

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newRole: RoleConfiguration = {
        provider: LLMProvider.OPENROUTER,
        available_models: ['anthropic/claude-3-sonnet'],
        selected_model: 'anthropic/claude-3-sonnet',
        api_key: '',
        cost_limit: 50.0,
        max_tokens: 4096,
        temperature: 0.7,
        enabled: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      setRoles(prev => ({ ...prev, [roleName]: newRole }));
      setActiveRole(roleName);
      setNewRoleName('');
      setShowCreateForm(false);
      setErrors(prev => ({ ...prev, create: null }));

    } catch (error) {
      setErrors(prev => ({
        ...prev,
        create: {
          message: 'Failed to create role',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      }));
    } finally {
      setIsLoading(prev => ({ ...prev, create: false }));
    }
  };

  // Handle role deletion
  const handleDeleteRole = async (roleName: string) => {
    if (Object.keys(roles).length <= 1) {
      setErrors(prev => ({
        ...prev,
        delete: { message: 'Cannot delete the last role' },
      }));
      return;
    }

    setIsLoading(prev => ({ ...prev, delete: true }));

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newRoles = { ...roles };
      delete newRoles[roleName];
      setRoles(newRoles);

      // Switch to first remaining role if deleted role was active
      if (activeRole === roleName) {
        setActiveRole(Object.keys(newRoles)[0]);
      }

      setShowDeleteModal(null);
      setErrors(prev => ({ ...prev, delete: null }));

    } catch (error) {
      setErrors(prev => ({
        ...prev,
        delete: {
          message: 'Failed to delete role',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      }));
    } finally {
      setIsLoading(prev => ({ ...prev, delete: false }));
    }
  };

  // Get role display name
  const getRoleDisplayName = (roleName: string) => {
    return roleName
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  if (isLoading.global) {
    return (
      <div className={clsx('flex items-center justify-center min-h-96', className)}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading role configurations...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={clsx('max-w-7xl mx-auto', className)}>
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Role Management</h1>
            <p className="mt-2 text-gray-600">
              Configure LLM providers and models for each AI agent role
            </p>
          </div>

          <button
            onClick={() => setShowCreateForm(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Role
          </button>
        </div>
      </div>

      {/* Role tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8" aria-label="Roles">
            {Object.keys(roles).map(roleName => (
              <button
                key={roleName}
                onClick={() => setActiveRole(roleName)}
                className={clsx(
                  'py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap',
                  {
                    'border-blue-500 text-blue-600': activeRole === roleName,
                    'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300':
                      activeRole !== roleName,
                  }
                )}
              >
                <div className="flex items-center space-x-2">
                  <span>{getRoleDisplayName(roleName)}</span>

                  {/* Status indicators */}
                  <div className="flex items-center space-x-1">
                    {roles[roleName].enabled ? (
                      <CheckCircleIcon className="h-4 w-4 text-green-500" />
                    ) : (
                      <ExclamationTriangleIcon className="h-4 w-4 text-yellow-500" />
                    )}

                    {errors[roleName] && (
                      <ExclamationTriangleIcon className="h-4 w-4 text-red-500" />
                    )}
                  </div>

                  {/* Delete button */}
                  {Object.keys(roles).length > 1 && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setShowDeleteModal(roleName);
                      }}
                      className="ml-2 text-gray-400 hover:text-red-500"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Active role configuration */}
      {activeRole && roles[activeRole] && (
        <RoleConfigPanel
          roleName={activeRole}
          configuration={roles[activeRole]}
          onConfigurationChange={(updates) =>
            handleConfigurationChange(activeRole, updates)
          }
          onSave={() => handleSave(activeRole)}
          isLoading={isLoading[activeRole]}
          error={errors[activeRole]}
        />
      )}

      {/* Create role modal */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <h3 className="text-lg font-bold text-gray-900 mb-4">Create New Role</h3>

            <div className="space-y-4">
              <div>
                <label htmlFor="role-name" className="block text-sm font-medium text-gray-700">
                  Role Name
                </label>
                <input
                  id="role-name"
                  type="text"
                  value={newRoleName}
                  onChange={(e) => setNewRoleName(e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  placeholder="e.g., Code Reviewer"
                />
              </div>

              {errors.create && (
                <div className="text-sm text-red-600">
                  {errors.create.message}
                </div>
              )}
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowCreateForm(false);
                  setNewRoleName('');
                  setErrors(prev => ({ ...prev, create: null }));
                }}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateRole}
                disabled={!newRoleName.trim() || isLoading.create}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:bg-gray-400"
              >
                {isLoading.create ? 'Creating...' : 'Create Role'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Delete confirmation modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <h3 className="text-lg font-bold text-gray-900 mb-4">Delete Role</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete the "{getRoleDisplayName(showDeleteModal)}" role?
              This action cannot be undone.
            </p>

            {errors.delete && (
              <div className="text-sm text-red-600 mb-4">
                {errors.delete.message}
              </div>
            )}

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowDeleteModal(null);
                  setErrors(prev => ({ ...prev, delete: null }));
                }}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
              >
                Cancel
              </button>
              <button
                onClick={() => handleDeleteRole(showDeleteModal)}
                disabled={isLoading.delete}
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 disabled:bg-gray-400"
              >
                {isLoading.delete ? 'Deleting...' : 'Delete Role'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ConfigManager;