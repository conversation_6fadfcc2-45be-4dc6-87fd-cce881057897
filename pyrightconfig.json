{"include": ["containers/*/src", "scripts", "tests"], "exclude": ["**/__pycache__", "**/node_modules", "**/.pytest_cache", "**/venv", "**/env"], "reportMissingImports": "none", "reportMissingModuleSource": "warning", "reportOptionalMemberAccess": "none", "reportOptionalSubscript": "none", "reportGeneralTypeIssues": "error", "reportUnknownMemberType": "none", "reportUnknownArgumentType": "none", "reportUnknownVariableType": "none", "reportImportCycles": "warning", "reportUnusedImport": "warning", "reportUnusedClass": "warning", "reportUnusedFunction": "warning", "reportUnusedVariable": "warning", "reportDuplicateImport": "warning", "typeCheckingMode": "basic", "pythonVersion": "3.9", "pythonPlatform": "Linux", "executionEnvironments": [{"root": "containers/ai-orchestrator", "pythonVersion": "3.9", "pythonPlatform": "Linux", "extraPaths": ["containers/ai-orchestrator/src"]}, {"root": "containers/user-portal", "pythonVersion": "3.9", "pythonPlatform": "Linux"}]}