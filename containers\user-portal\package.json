{"name": "user-portal", "version": "1.0.0", "description": "User Portal for Role-based LLM Management in AI Coding Agent", "main": "index.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "analyze": "cross-env ANALYZE=true npm run build", "fix-symlinks": "chmod +x fix-symlinks.sh && ./fix-symlinks.sh", "dev:safe": "npm run fix-symlinks && npm run dev"}, "dependencies": {"@heroicons/react": "^2.0.18", "@supabase/supabase-js": "^2.45.0", "axios": "^1.6.2", "clsx": "^2.0.0", "next": "^14.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-select": "^5.8.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/node": "24.3.0", "@types/react": "19.1.11", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "autoprefixer": "^10.4.16", "cross-env": "^7.0.3", "eslint": "^8.54.0", "eslint-config-next": "^14.0.3", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.32", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "tailwindcss": "^3.3.6", "typescript": "5.9.2"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "optionalDependencies": {"@next/swc-linux-x64-gnu": "*"}}