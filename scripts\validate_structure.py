#!/usr/bin/env python3
"""
Directory structure validator for AI Coding Agent Project.
Ensures the project structure follows the expected layout defined in the roadmap.
"""

import os
import sys
from pathlib import Path

def validate_directory_structure():
    """Validate that the directory structure matches expected layout."""
    project_root = Path(__file__).parent.parent
    errors = []

    # Define required directories and files
    required_structure = {
        'directories': [
            'containers',
            'containers/user-portal',
            'containers/ai-orchestrator',
            'containers/code-server',
            'docs',
            'scripts',
            'tests',
        ],
        'files': [
            '.env.example',
            'docker-compose.yml',
            'README.md',
            'docs/Projectroadmap.md',
            'scripts/setup.sh',
            'scripts/start-dev.sh',
            'scripts/deploy.sh',
        ]
    }

    # Check required directories
    for directory in required_structure['directories']:
        dir_path = project_root / directory
        if not dir_path.exists():
            errors.append(f"Missing required directory: {directory}")
        elif not dir_path.is_dir():
            errors.append(f"Expected directory but found file: {directory}")

    # Check required files
    for file_path in required_structure['files']:
        full_path = project_root / file_path
        if not full_path.exists():
            errors.append(f"Missing required file: {file_path}")
        elif not full_path.is_file():
            errors.append(f"Expected file but found directory: {file_path}")

    # Check container-specific structure
    container_dirs = ['user-portal', 'ai-orchestrator', 'code-server']
    # Note: ollama and postgresql use official images, no custom Dockerfile needed
    for container in container_dirs:
        container_path = project_root / 'containers' / container
        if container_path.exists():
            # Each container should have a Dockerfile
            dockerfile_path = container_path / 'Dockerfile'
            if not dockerfile_path.exists():
                errors.append(f"Missing Dockerfile in container: {container}")

    # Check Python containers for required files
    python_containers = ['ai-orchestrator']  # admin-dashboard is Node.js, not Python
    for container in python_containers:
        container_path = project_root / 'containers' / container
        if container_path.exists():
            requirements_path = container_path / 'requirements.txt'
            if not requirements_path.exists():
                errors.append(f"Missing requirements.txt in Python container: {container}")

    # Check that Python containers have proper package structure
    python_src_containers = ['ai-orchestrator']  # admin-dashboard is Node.js
    for container in python_src_containers:
        src_path = project_root / 'containers' / container / 'src'
        if src_path.exists():
            init_path = src_path / '__init__.py'
            if not init_path.exists():
                errors.append(f"Missing __init__.py in Python container src: {container}")

    return errors

def validate_naming_conventions():
    """Validate naming conventions for the project structure."""
    project_root = Path(__file__).parent.parent
    errors = []

    # Check that directories use hyphens (except Python packages and node_modules)
    for root, dirs, files in os.walk(project_root):
        # Skip hidden directories, virtual environments, and node_modules
        dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['venv', '__pycache__', 'node_modules']]

        # Skip if we're inside node_modules
        if 'node_modules' in Path(root).parts:
            continue

        for directory in dirs:
            dir_path = Path(root) / directory
            # Skip Python package directories (they can use underscores)
            if (dir_path / '__init__.py').exists():
                continue

            # Check for proper naming (lowercase, hyphens)
            # Allow underscores for certain technical directories
            allowed_special_dirs = {'.git', 'node_modules', '__pycache__', '__tests__'}
            if directory not in allowed_special_dirs:
                if not all(c.islower() or c.isdigit() or c in '-_.' for c in directory):
                    errors.append(f"Directory should use lowercase and hyphens: {dir_path}")

    return errors

def main():
    """Main validation function."""
    print("Validating directory structure...")
    structure_errors = validate_directory_structure()
    naming_errors = validate_naming_conventions()

    all_errors = structure_errors + naming_errors

    if all_errors:
        print("❌ Structure validation failed:")
        for error in all_errors:
            print(f"  - {error}")
        sys.exit(1)
    else:
        print("✅ Directory structure is valid!")
        sys.exit(0)

if __name__ == "__main__":
    main()
