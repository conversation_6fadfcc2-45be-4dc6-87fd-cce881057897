/**
 * Health Check API Route
 * Provides health status for Docker health checks and monitoring
 */

import type { NextApiRequest, NextApiResponse } from 'next';

interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  version: string;
  uptime: number;
  checks: {
    api: 'healthy' | 'unhealthy' | 'degraded';
    memory: 'healthy' | 'unhealthy';
    dependencies: 'healthy' | 'unhealthy' | 'degraded';
  };
  info?: {
    memoryUsage: NodeJS.MemoryUsage;
    nodeVersion: string;
    environment: string;
  };
}

/**
 * Check API connectivity to backend
 */
async function checkApiConnectivity(skipApiCheck: boolean = false): Promise<'healthy' | 'unhealthy' | 'degraded'> {
  // Skip API check for Docker health checks to avoid circular dependency
  if (skipApiCheck) {
    return 'healthy';
  }

  const apiBaseUrl = process.env.API_BASE_URL || process.env.NEXT_PUBLIC_API_BASE_URL;

  if (!apiBaseUrl) {
    return 'degraded'; // Configuration issue but not fatal
  }

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 3000); // 3 second timeout

    const response = await fetch(`${apiBaseUrl}/health`, {
      method: 'GET',
      signal: controller.signal,
      headers: {
        'User-Agent': 'AdminDashboard-HealthCheck',
      },
    });

    clearTimeout(timeoutId);

    if (response.ok) {
      return 'healthy';
    } else {
      return 'degraded';
    }
  } catch (error) {
    console.error('Health check API connectivity failed:', error);
    return 'degraded'; // Don't fail health check due to API connectivity issues
  }
}

/**
 * Check memory usage
 */
function checkMemoryUsage(): 'healthy' | 'unhealthy' {
  const memUsage = process.memoryUsage();
  const usedMemoryMB = memUsage.heapUsed / 1024 / 1024;

  // Very lenient threshold - only fail if using more than 2GB (extremely high)
  // This is primarily to catch severe memory leaks, not normal usage
  if (usedMemoryMB > 2048) {
    return 'unhealthy';
  }

  return 'healthy';
}

/**
 * Check critical dependencies
 */
async function checkDependencies(): Promise<'healthy' | 'unhealthy' | 'degraded'> {
  // For a Next.js frontend, dependencies are mostly build-time
  // We'll check if critical runtime dependencies are available

  try {
    // Check if React is properly loaded
    if (typeof require('react') !== 'object') {
      return 'unhealthy';
    }

    // Check if Next.js runtime is available
    if (typeof require('next/server') !== 'object') {
      return 'unhealthy';
    }

    return 'healthy';
  } catch (error) {
    console.error('Dependency check failed:', error);
    return 'unhealthy';
  }
}

/**
 * Health Check Handler
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<HealthCheckResponse>
) {
  const startTime = Date.now();

  try {
    // Only allow GET requests
    if (req.method !== 'GET') {
      return res.status(405).json({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        version: process.env.npm_package_version || '1.0.0',
        uptime: process.uptime(),
        checks: {
          api: 'unhealthy',
          memory: 'unhealthy',
          dependencies: 'unhealthy',
        },
      });
    }

    // Detect if this is a Docker health check (skip external API checks)
    const userAgent = req.headers['user-agent'] || '';
    const isDockerHealthCheck = userAgent.includes('Go-http-client') ||
                                req.headers['x-forwarded-for'] === undefined;

    // Perform health checks
    const [apiCheck, memoryCheck, dependenciesCheck] = await Promise.all([
      checkApiConnectivity(isDockerHealthCheck),
      Promise.resolve(checkMemoryUsage()),
      checkDependencies(),
    ]);

    // Determine overall health status
    const isHealthy =
      (apiCheck === 'healthy' || apiCheck === 'degraded') && // API can be degraded
      memoryCheck === 'healthy' &&
      dependenciesCheck === 'healthy';

    const status = isHealthy ? 'healthy' : 'unhealthy';
    const httpStatus = isHealthy ? 200 : 503;

    const response: HealthCheckResponse = {
      status,
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      uptime: process.uptime(),
      checks: {
        api: apiCheck,
        memory: memoryCheck,
        dependencies: dependenciesCheck,
      },
    };

    // Always include detailed info for debugging
    response.info = {
      memoryUsage: process.memoryUsage(),
      nodeVersion: process.version,
      environment: process.env.NODE_ENV || 'unknown',
    };

    // Set appropriate cache headers
    if (isHealthy) {
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    } else {
      res.setHeader('Cache-Control', 'no-cache');
    }

    // Add response time header
    res.setHeader('X-Response-Time', `${Date.now() - startTime}ms`);

    return res.status(httpStatus).json(response);

  } catch (error) {
    console.error('Health check failed with error:', error);

    const errorResponse: HealthCheckResponse = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      uptime: process.uptime(),
      checks: {
        api: 'unhealthy',
        memory: 'unhealthy',
        dependencies: 'unhealthy',
      },
    };

    return res.status(503).json(errorResponse);
  }
}