#!/bin/bash
# Bash script to reset user-portal development environment
# Run this from the project root directory

echo "=== Phase 2: Resetting Development Environment State ==="

# Step 1: Stop and remove all services
echo "Step 1: Stopping and removing all services..."
docker-compose -f docker-compose.yml -f docker-compose.dev.yml down

# Step 2: Remove specific named volumes to ensure clean slate
echo "Step 2: Removing corrupted named volumes..."
docker volume rm ai-coding-agent-dev_user_portal_node_modules 2>/dev/null || true
docker volume rm ai-coding-agent-dev_user_portal_next 2>/dev/null || true

# Step 3: Remove any orphaned containers
echo "Step 3: Removing orphaned containers..."
docker-compose -f docker-compose.yml -f docker-compose.dev.yml down --remove-orphans

# Step 4: Prune any dangling images (optional but recommended)
echo "Step 4: Cleaning up dangling images..."
docker image prune -f

# Step 5: Rebuild user-portal service with hardened Dockerfile
echo "Step 5: Rebuilding user-portal service..."
docker-compose -f docker-compose.yml -f docker-compose.dev.yml build --no-cache user-portal

# Step 6: Start the user-portal service
echo "Step 6: Starting user-portal service..."
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d user-portal

# Step 7: Show logs to verify successful startup
echo "Step 7: Showing user-portal logs..."
sleep 5
docker-compose -f docker-compose.yml -f docker-compose.dev.yml logs user-portal

echo "=== Reset Complete! ==="
echo "User Portal should now be accessible at: http://portal.localhost"
echo "To continue monitoring logs: docker-compose -f docker-compose.yml -f docker-compose.dev.yml logs -f user-portal"