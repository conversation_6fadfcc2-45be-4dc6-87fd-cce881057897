# Start from the official Node.js Alpine image
FROM node:20-alpine

# Update npm to latest version as root (before switching to node user)
RUN npm install -g npm@latest

# Set the working directory and ensure it's owned by node user
WORKDIR /home/<USER>/app
RUN chown -R node:node /home/<USER>/app

# --- THIS IS THE KEY ---
# Copy package manager files as the ROOT user first to set permissions
COPY --chown=node:node package*.json ./

# Switch to the non-root 'node' user for ALL subsequent operations
USER node

# As the 'node' user, install dependencies. This ensures node_modules is owned by 'node'.
RUN npm install --legacy-peer-deps

# Copy the rest of the application source code with correct ownership
COPY --chown=node:node . .

# Expose the application port
EXPOSE 3000

# The command to run the development server
CMD ["npm", "run", "dev"]
