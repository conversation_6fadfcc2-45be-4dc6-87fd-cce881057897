# Start from the official Node.js Alpine image
FROM node:20-alpine

# Set the working directory
WORKDIR /home/<USER>/app

# --- THIS IS THE KEY ---
# Copy package manager files as the ROOT user first to set permissions
COPY --chown=node:node package*.json ./

# Switch to the non-root 'node' user for ALL subsequent operations
USER node

# As the 'node' user, install dependencies. This ensures node_modules is owned by 'node'.
RUN npm install --legacy-peer-deps

# Copy the rest of the application source code. It will be automatically owned by 'node'.
COPY . .

# Expose the application port
EXPOSE 3000

# The command to run the development server
CMD ["npm", "run", "dev"]
