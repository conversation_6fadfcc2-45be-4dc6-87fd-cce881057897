#!/bin/bash
# Alternative security scanning with Trivy
# More widely available than Docker Scout

set -e

echo "🔍 Security Scanning with Trivy..."

# Check if Trivy is installed
if ! command -v trivy &> /dev/null; then
    echo "❌ Trivy is not installed"
    echo "Please install Trivy:"
    echo "  - macOS: brew install trivy"
    echo "  - Linux: See https://aquasecurity.github.io/trivy/latest/getting-started/installation/"
    echo "  - Windows: Use WSL or Docker: docker run --rm -v /var/run/docker.sock:/var/run/docker.sock aquasec/trivy"
    exit 1
fi

# Create reports directory
mkdir -p ./security-reports

# Define images to scan
IMAGES=(
    "ai-orchestrator"
    "user-portal"
    "code-server"
    "postgresql"
    "ollama"
)

SCAN_FAILED=false

echo "📦 Scanning container images..."

# Scan each image
for image in "${IMAGES[@]}"; do
    echo "🔍 Scanning $image..."

    # Build image for scanning
    if ! docker build -t "codingagenttwo-$image:scan" "./containers/$image/" > /dev/null 2>&1; then
        echo "❌ Failed to build $image"
        SCAN_FAILED=true
        continue
    fi

    # Scan for vulnerabilities
    if trivy image --format sarif --output "./security-reports/$image-trivy.sarif" "codingagenttwo-$image:scan" 2>/dev/null; then
        echo "✅ Trivy scan completed for $image"
    else
        echo "⚠️ Trivy scan failed for $image"
        SCAN_FAILED=true
    fi

    # Generate human-readable report
    trivy image --format table --output "./security-reports/$image-report.txt" "codingagenttwo-$image:scan" 2>/dev/null || true

    echo ""
done

# Scan Dockerfiles
echo "📄 Scanning Dockerfiles..."
for image in "${IMAGES[@]}"; do
    echo "🔍 Scanning Dockerfile for $image..."
    if trivy config --format sarif --output "./security-reports/$image-dockerfile.sarif" "./containers/$image/Dockerfile" 2>/dev/null; then
        echo "✅ Dockerfile scan completed for $image"
    else
        echo "⚠️ Dockerfile scan failed for $image"
    fi
done

# Generate summary
cat > ./security-reports/trivy-summary.md << EOF
# Trivy Security Scan Summary

Generated: $(date)

## Scanned Components

### Container Images
$(for image in "${IMAGES[@]}"; do echo "- $image"; done)

### Dockerfiles
$(for image in "${IMAGES[@]}"; do echo "- containers/$image/Dockerfile"; done)

## Report Files

- SARIF reports: \`*-trivy.sarif\`, \`*-dockerfile.sarif\`
- Human-readable reports: \`*-report.txt\`

## Security Best Practices Applied

1. ✅ Non-root users in all containers
2. ✅ Minimal base images (alpine, slim)
3. ✅ Security updates installed
4. ✅ .dockerignore files to reduce attack surface
5. ✅ BuildKit cache mounting for reproducible builds

## Recommendations

- Review SARIF reports in VS Code with SARIF extension
- Update base images regularly
- Monitor CVE databases for new vulnerabilities
- Consider distroless images for production workloads
EOF

if [ "$SCAN_FAILED" = true ]; then
    echo "❌ Some scans failed. Check individual reports for details."
    exit 1
else
    echo "✅ All Trivy scans completed successfully!"
    echo "📄 Reports saved in ./security-reports/"
    exit 0
fi