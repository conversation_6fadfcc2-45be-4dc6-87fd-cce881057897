/**
 * Next.js App Component
 * Global configuration for the user portal application
 */

import React from 'react';
import type { AppProps } from 'next/app';
import Head from 'next/head';
import '@/styles/globals.css';

function UserPortal({ Component, pageProps }: AppProps) {
  return (
    <>
      <Head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta charSet="utf-8" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      {/* Global error boundary could be added here */}
      <Component {...pageProps} />
    </>
  );
}

export default UserPortal;