/**
 * useProviderModels Hook
 * Custom hook for managing LLM provider models and capabilities
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { roleApi } from '@/lib/api';
import { LLMProvider } from '@/types/role';
import type {
  ProviderModelsResponse,
  ModelOption,
  ProviderOption,
  ApiError,
} from '@/types/role';
import type { LoadingState } from '@/types/api';

interface UseProviderModelsReturn {
  // State
  providers: ProviderOption[];
  modelsByProvider: Record<LLMProvider, ModelOption[]>;
  loading: boolean;
  error: ApiError | null;

  // Actions
  refreshProviderModels: () => Promise<void>;
  clearError: () => void;

  // Utilities
  getModelsForProvider: (provider: LLMProvider) => ModelOption[];
  getProviderInfo: (provider: LLMProvider) => ProviderOption | null;
  isProviderSupported: (provider: LLMProvider) => boolean;
  getModelInfo: (provider: LLMProvider, modelValue: string) => ModelOption | null;
}

// Static provider information
const PROVIDER_OPTIONS: ProviderOption[] = [
  {
    value: LLMProvider.OLLAMA,
    label: 'Ollama',
    requiresApiKey: false,
    description: 'Local LLM serving with Ollama - no API key required',
  },
  {
    value: LLMProvider.OPENROUTER,
    label: 'OpenRouter',
    requiresApiKey: true,
    description: 'Access multiple AI models through OpenRouter API',
  },
  {
    value: LLMProvider.OPENAI,
    label: 'OpenAI',
    requiresApiKey: true,
    description: 'GPT-4, GPT-3.5-Turbo, and other OpenAI models',
  },
  {
    value: LLMProvider.ANTHROPIC,
    label: 'Anthropic',
    requiresApiKey: true,
    description: 'Claude 3 Sonnet, Haiku, and Opus models',
  },
];

// Fallback models if API is unavailable
const FALLBACK_MODELS: Record<LLMProvider, ModelOption[]> = {
  [LLMProvider.OLLAMA]: [
    {
      value: 'llama2:7b',
      label: 'Llama 2 7B',
      description: 'Efficient 7B parameter model for general tasks',
      context_length: 4096,
    },
    {
      value: 'llama2:13b',
      label: 'Llama 2 13B',
      description: 'Balanced 13B parameter model with good performance',
      context_length: 4096,
    },
    {
      value: 'codellama:7b',
      label: 'CodeLlama 7B',
      description: 'Specialized code generation model',
      context_length: 16384,
    },
    {
      value: 'codellama:13b',
      label: 'CodeLlama 13B',
      description: 'Larger code generation model with better accuracy',
      context_length: 16384,
    },
    {
      value: 'deepseek-coder:6.7b',
      label: 'DeepSeek Coder 6.7B',
      description: 'Advanced code understanding and generation',
      context_length: 16384,
    },
  ],
  [LLMProvider.OPENROUTER]: [
    {
      value: 'anthropic/claude-3-sonnet',
      label: 'Claude 3 Sonnet',
      description: 'Balanced performance and cost for most tasks',
      cost_per_token: 0.000003,
      context_length: 200000,
    },
    {
      value: 'anthropic/claude-3-haiku',
      label: 'Claude 3 Haiku',
      description: 'Fast and cost-effective for simple tasks',
      cost_per_token: 0.00000025,
      context_length: 200000,
    },
    {
      value: 'openai/gpt-4',
      label: 'GPT-4',
      description: 'Most capable OpenAI model',
      cost_per_token: 0.00003,
      context_length: 8192,
    },
    {
      value: 'openai/gpt-4-turbo',
      label: 'GPT-4 Turbo',
      description: 'Faster GPT-4 with longer context',
      cost_per_token: 0.00001,
      context_length: 128000,
    },
    {
      value: 'openai/gpt-3.5-turbo',
      label: 'GPT-3.5 Turbo',
      description: 'Fast and cost-effective for most tasks',
      cost_per_token: 0.0000005,
      context_length: 16385,
    },
  ],
  [LLMProvider.OPENAI]: [
    {
      value: 'gpt-4',
      label: 'GPT-4',
      description: 'Most capable OpenAI model',
      cost_per_token: 0.00003,
      context_length: 8192,
    },
    {
      value: 'gpt-4-turbo',
      label: 'GPT-4 Turbo',
      description: 'Faster GPT-4 with longer context',
      cost_per_token: 0.00001,
      context_length: 128000,
    },
    {
      value: 'gpt-4o',
      label: 'GPT-4o',
      description: 'Optimized GPT-4 for better performance',
      cost_per_token: 0.000005,
      context_length: 128000,
    },
    {
      value: 'gpt-3.5-turbo',
      label: 'GPT-3.5 Turbo',
      description: 'Fast and cost-effective',
      cost_per_token: 0.0000005,
      context_length: 16385,
    },
  ],
  [LLMProvider.ANTHROPIC]: [
    {
      value: 'claude-3-opus',
      label: 'Claude 3 Opus',
      description: 'Most capable Claude model for complex tasks',
      cost_per_token: 0.000015,
      context_length: 200000,
    },
    {
      value: 'claude-3-sonnet',
      label: 'Claude 3 Sonnet',
      description: 'Balanced performance and cost',
      cost_per_token: 0.000003,
      context_length: 200000,
    },
    {
      value: 'claude-3-haiku',
      label: 'Claude 3 Haiku',
      description: 'Fast and cost-effective',
      cost_per_token: 0.00000025,
      context_length: 200000,
    },
  ],
};

/**
 * Custom hook for provider models management
 */
export const useProviderModels = (): UseProviderModelsReturn => {
  const [modelsByProvider, setModelsByProvider] = useState<Record<LLMProvider, ModelOption[]>>(FALLBACK_MODELS);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<ApiError | null>(null);

  /**
   * Transform API response to ModelOption format
   */
  const transformApiResponse = useCallback((responses: ProviderModelsResponse[]): Record<LLMProvider, ModelOption[]> => {
    const result: Record<LLMProvider, ModelOption[]> = {
      [LLMProvider.OLLAMA]: [],
      [LLMProvider.OPENROUTER]: [],
      [LLMProvider.OPENAI]: [],
      [LLMProvider.ANTHROPIC]: [],
    };

    responses.forEach(response => {
      if (response.provider && response.models) {
        result[response.provider] = response.models.map(model => ({
          value: model,
          label: model.split('/').pop() || model, // Extract model name from path
          description: `${response.provider} model: ${model}`,
        }));
      }
    });

    return result;
  }, []);

  /**
   * Fetch provider models from API
   */
  const refreshProviderModels = useCallback(async (): Promise<void> => {
    setLoading(true);
    setError(null);

    try {
      const response = await roleApi.getProviderModels();

      if (response.error) {
        // Use fallback models if API fails
        console.warn('Failed to fetch provider models, using fallback data:', response.error);
        setModelsByProvider(FALLBACK_MODELS);
        setError(response.error);
        return;
      }

      if (response.data && response.data.length > 0) {
        const transformedModels = transformApiResponse(response.data);
        setModelsByProvider(transformedModels);
      } else {
        // Use fallback if no data returned
        setModelsByProvider(FALLBACK_MODELS);
      }
    } catch (err) {
      console.warn('Error fetching provider models, using fallback data:', err);
      setModelsByProvider(FALLBACK_MODELS);
      setError({
        message: 'Failed to fetch provider models',
        details: err instanceof Error ? err.message : 'Unknown error',
      });
    } finally {
      setLoading(false);
    }
  }, [transformApiResponse]);

  /**
   * Clear the current error
   */
  const clearError = useCallback((): void => {
    setError(null);
  }, []);

  /**
   * Get models for a specific provider
   */
  const getModelsForProvider = useCallback((provider: LLMProvider): ModelOption[] => {
    return modelsByProvider[provider] || [];
  }, [modelsByProvider]);

  /**
   * Get provider information
   */
  const getProviderInfo = useCallback((provider: LLMProvider): ProviderOption | null => {
    return PROVIDER_OPTIONS.find(p => p.value === provider) || null;
  }, []);

  /**
   * Check if provider is supported
   */
  const isProviderSupported = useCallback((provider: LLMProvider): boolean => {
    return PROVIDER_OPTIONS.some(p => p.value === provider);
  }, []);

  /**
   * Get specific model information
   */
  const getModelInfo = useCallback((provider: LLMProvider, modelValue: string): ModelOption | null => {
    const providerModels = modelsByProvider[provider] || [];
    return providerModels.find(model => model.value === modelValue) || null;
  }, [modelsByProvider]);

  // Load provider models on mount
  useEffect(() => {
    refreshProviderModels();
  }, [refreshProviderModels]);

  // Memoized providers list
  const providers = useMemo(() => PROVIDER_OPTIONS, []);

  return {
    // State
    providers,
    modelsByProvider,
    loading,
    error,

    // Actions
    refreshProviderModels,
    clearError,

    // Utilities
    getModelsForProvider,
    getProviderInfo,
    isProviderSupported,
    getModelInfo,
  };
};

export default useProviderModels;