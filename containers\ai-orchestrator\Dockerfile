# syntax=docker/dockerfile:1
# Multi-stage Dockerfile with security best practices and BuildKit optimization
# This creates a minimal production image without build tools

# ============================================================================
# BUILDER STAGE: Install dependencies and build tools
# ============================================================================
FROM python:3.11.7-slim-bullseye AS builder

# Prevent debconf warnings during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Install build dependencies in builder stage only
RUN apt-get update && apt-get install -y \
  build-essential \
  curl \
  ca-certificates \
  && apt-get upgrade -y \
  && rm -rf /var/lib/apt/lists/*

# Update pip to latest version
RUN pip install --upgrade pip

# Set working directory for builder
WORKDIR /app

# Copy requirements file
COPY requirements.txt .

# Install dependencies to user directory for easy copying
RUN --mount=type=cache,target=/root/.cache/pip \
  pip install --user --no-cache-dir --root-user-action=ignore -r requirements.txt

# ============================================================================
# PRODUCTION STAGE: Minimal runtime image
# ============================================================================
FROM python:3.11.7-slim-bullseye AS production

# Prevent debconf warnings during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Install only essential runtime dependencies (no build tools)
RUN apt-get update && apt-get install -y \
  curl \
  ca-certificates \
  && apt-get upgrade -y \
  && rm -rf /var/lib/apt/lists/* \
  && apt-get clean

# Create non-root user for security
RUN useradd -m appuser

# Set working directory
WORKDIR /app

# Copy Python packages from builder stage
COPY --from=builder --chown=appuser:appuser /root/.local /home/<USER>/.local

# Copy application source code
COPY --chown=appuser:appuser src ./src

# Include Alembic configuration and migration scripts in the image
COPY --chown=appuser:appuser alembic.ini ./alembic.ini
COPY --chown=appuser:appuser alembic ./alembic

# Switch to non-root user for security
USER appuser

# Ensure Python packages are in PATH
ENV PATH=/home/<USER>/.local/bin:$PATH

# Expose application port
EXPOSE 8000

# Add health check for FastAPI service monitoring
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD python -c "import urllib.request; urllib.request.urlopen('http://localhost:8000/health', timeout=5)" || exit 1

# Set security and project labels
LABEL org.opencontainers.image.title="AI Coding Agent - Orchestrator" \
  org.opencontainers.image.description="FastAPI-based AI agent orchestration service" \
  org.opencontainers.image.vendor="AI Coding Agent Project" \
  maintainer="AI Coding Agent Team" \
  security.non-root="true" \
  security.user="appuser"

# Set the command to run application using exec form for proper signal handling
CMD ["uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000"]