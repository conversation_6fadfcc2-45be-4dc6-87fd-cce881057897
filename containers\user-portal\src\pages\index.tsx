// c:\Users\<USER>\Desktop\codingagenttwo\containers\admin-dashboard\src\pages\index.tsx
import React, { useEffect, useMemo, useState } from 'react';
import Head from 'next/head';
import type { NextPage } from 'next';
import { withAuth, type WithAuthInjectedProps } from '@/components/withAuth';
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  PlusIcon,
  TrashIcon,
  CogIcon,
} from '@heroicons/react/24/outline';
import clsx from 'clsx';

import ProviderSelector from '@/components/ProviderSelector';
import ModelSelector from '@/components/ModelSelector';
import ApiKeyInput from '@/components/ApiKeyInput';

import { useRoles } from '@/hooks/useRoles';
import { useProviderModels } from '@/hooks/useProviderModels';

import {
  LLMProvider,
  RoleConfiguration,
  RoleConfigurationUpdate,
} from '@/types/role';
import { LoadingState } from '@/types/api';

type HomeProps = WithAuthInjectedProps;

const Home: NextPage<HomeProps> = ({ user, logout }) => {
  // Hooks: live data from backend
  const {
    roles,
    loading,
    error,
    refreshRoles,
    createRole,
    updateRole,
    deleteRole,
    clearError,
  } = useRoles();

  const {
    providers,
    getModelsForProvider,
    isProviderSupported,
  } = useProviderModels();

  // Local UI state
  const roleNames = useMemo(() => Object.keys(roles || {}), [roles]);
  const [activeRole, setActiveRole] = useState<string>('');
  const [isSaving, setIsSaving] = useState<boolean>(false);

  // Create role modal
  const [showCreateForm, setShowCreateForm] = useState<boolean>(false);
  const [newRoleName, setNewRoleName] = useState<string>('');
  const [createError, setCreateError] = useState<string | null>(null);

  // Delete confirmation
  const [showDeleteModal, setShowDeleteModal] = useState<string | null>(null);
  const [deleteError, setDeleteError] = useState<string | null>(null);

  // Form state for the active role configuration
  type FormState = {
    provider: LLMProvider;
    selected_model: string;
    api_key: string;
    cost_limit: number | null;
    max_tokens: number;
    temperature: number;
    enabled: boolean;
  };
  const [form, setForm] = useState<FormState | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState<boolean>(false);

  // Initialize active role when roles load
  useEffect(() => {
    if (roleNames.length > 0) {
      setActiveRole(prev => (prev && roles[prev] ? prev : roleNames[0]));
    } else {
      setActiveRole('');
      setForm(null);
    }
  }, [roleNames, roles]);

  // Sync form with active role configuration
  useEffect(() => {
    if (!activeRole || !roles[activeRole]) {
      setForm(null);
      return;
    }
    const cfg = roles[activeRole] as RoleConfiguration;

    setForm({
      provider: cfg.provider,
      selected_model: cfg.selected_model || '',
      api_key: cfg.api_key || '',
      cost_limit: cfg.cost_limit ?? null,
      max_tokens: cfg.max_tokens ?? 4096,
      temperature: cfg.temperature ?? 0.7,
      enabled: cfg.enabled !== false,
    });
    setHasUnsavedChanges(false);
  }, [activeRole, roles]);

  // Utilities
  const getRoleDisplayName = (roleName: string) =>
    roleName
      .split('_')
      .map(w => w.charAt(0).toUpperCase() + w.slice(1))
      .join(' ');

  const onProviderChange = (provider: LLMProvider) => {
    if (!form) return;
    const next: FormState = {
      ...form,
      provider,
      // Clear selected model on provider change
      selected_model: '',
      // For Ollama, API key is not required
      api_key: provider === LLMProvider.OLLAMA ? '' : form.api_key,
    };
    setForm(next);
    setHasUnsavedChanges(true);
  };

  const onModelChange = (model: string) => {
    if (!form) return;
    setForm({ ...form, selected_model: model });
    setHasUnsavedChanges(true);
  };

  const onApiKeyChange = (apiKey: string) => {
    if (!form) return;
    setForm({ ...form, api_key: apiKey });
    setHasUnsavedChanges(true);
  };

  const onNumericChange = (key: keyof Pick<FormState, 'cost_limit' | 'max_tokens' | 'temperature'>) =>
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (!form) return;
      const value = e.target.value;
      let parsed: number | null = null;

      if (value === '') {
        parsed = key === 'cost_limit' ? null : 0;
      } else {
        parsed = key === 'cost_limit' ? parseFloat(value) : parseFloat(value);
      }

      setForm({ ...form, [key]: key === 'cost_limit' ? parsed : Number(parsed ?? 0) } as FormState);
      setHasUnsavedChanges(true);
    };

  const onEnabledToggle = () => {
    if (!form) return;
    setForm({ ...form, enabled: !form.enabled });
    setHasUnsavedChanges(true);
  };

  // Save updates
  const handleSave = async () => {
    if (!activeRole || !form) return;

    setIsSaving(true);
    setDeleteError(null);

    // Build updates payload
    const updates: RoleConfigurationUpdate = {
      provider: form.provider,
      selected_model: form.selected_model,
      // include available_models for consistency
      available_models: getModelsForProvider(form.provider).map(m => m.value),
      api_key: form.provider === LLMProvider.OLLAMA ? null : form.api_key || null,
      cost_limit: form.cost_limit ?? null,
      max_tokens: form.max_tokens,
      temperature: form.temperature,
      enabled: form.enabled,
    };

    const ok = await updateRole(activeRole, updates);
    setIsSaving(false);
    setHasUnsavedChanges(!ok);
  };

  // Reset form
  const handleReset = () => {
    if (!activeRole || !roles[activeRole]) return;
    const cfg = roles[activeRole];
    setForm({
      provider: cfg.provider,
      selected_model: cfg.selected_model || '',
      api_key: cfg.api_key || '',
      cost_limit: cfg.cost_limit ?? null,
      max_tokens: cfg.max_tokens ?? 4096,
      temperature: cfg.temperature ?? 0.7,
      enabled: cfg.enabled !== false,
    });
    setHasUnsavedChanges(false);
  };

  // Create role
  const handleCreateRole = async () => {
    setCreateError(null);
    const name = newRoleName.trim().toLowerCase().replace(/\s+/g, '_');
    if (!name) {
      setCreateError('Role name is required');
      return;
    }
    if (roles[name]) {
      setCreateError('Role name already exists');
      return;
    }

    // Default provider and models
    const defaultProvider = LLMProvider.OPENROUTER;
    const models = getModelsForProvider(defaultProvider);
    const selectedModel = models[0]?.value || '';

    const config: Omit<RoleConfiguration, 'created_at' | 'updated_at'> = {
      provider: defaultProvider,
      available_models: models.map(m => m.value),
      selected_model: selectedModel,
      api_key: '', // required for non-Ollama; user can fill it later
      cost_limit: 50.0,
      max_tokens: 4096,
      temperature: 0.7,
      enabled: true,
    };

    const ok = await createRole(name, config);
    if (!ok) {
      setCreateError('Failed to create role. Please try again.');
      return;
    }

    setShowCreateForm(false);
    setNewRoleName('');
    setCreateError(null);
    setActiveRole(name);
  };

  // Delete role
  const handleDeleteRole = async (roleName: string) => {
    setDeleteError(null);
    if (roleNames.length <= 1) {
      setDeleteError('Cannot delete the last role');
      return;
    }
    const ok = await deleteRole(roleName);
    if (!ok) {
      setDeleteError('Failed to delete role');
      return;
    }
    setShowDeleteModal(null);
  };

  // Loading / Error UI
  if (loading === LoadingState.LOADING || (!activeRole && loading !== LoadingState.ERROR)) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Head>
          <title>Role Management | AI Coding Agent Admin Dashboard</title>
          <meta name="description" content="Configure LLM providers and models for AI agent roles" />
          <link rel="icon" href="/favicon.ico" />
        </Head>
        <main className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-4 text-gray-600">Loading role configurations...</p>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Head>
        <title>Role Management | AI Coding Agent Admin Dashboard</title>
        <meta name="description" content="Configure LLM providers and models for AI agent roles" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="py-8 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Role Management</h1>
              <p className="mt-2 text-gray-600">Configure LLM providers and models for each AI agent role</p>
            </div>

            <div className="flex items-center gap-3">
              <span className="text-sm text-gray-600">{user.email}</span>
              <button
                onClick={logout}
                className="btn-secondary"
              >
                Logout
              </button>
              <button
                onClick={() => {
                  setShowCreateForm(true);
                  setCreateError(null);
                }}
                className="btn-primary"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Add Role
              </button>
            </div>
          </div>
        </div>

        {/* Global error */}
        {error && (
          <div className="alert alert-error mb-6">
            <div className="flex items-start">
              <ExclamationTriangleIcon className="h-5 w-5 mt-0.5" />
              <div className="ml-2">
                <p className="font-medium">Failed to load roles</p>
                <p className="text-sm">{error.message}{error.details ? ` - ${error.details}` : ''}</p>
                <div className="mt-2 space-x-2">
                  <button className="btn-secondary" onClick={() => { clearError(); refreshRoles(); }}>
                    Retry
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Role tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8" aria-label="Roles">
              {roleNames.map(roleName => (
                <button
                  key={roleName}
                  onClick={() => setActiveRole(roleName)}
                  className={clsx(
                    'py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap',
                    {
                      'border-blue-500 text-blue-600': activeRole === roleName,
                      'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300':
                        activeRole !== roleName,
                    }
                  )}
                >
                  <div className="flex items-center space-x-2">
                    <span>{getRoleDisplayName(roleName)}</span>
                    <div className="flex items-center space-x-1">
                      {roles[roleName]?.enabled !== false ? (
                        <CheckCircleIcon className="h-4 w-4 text-green-500" />
                      ) : (
                        <ExclamationTriangleIcon className="h-4 w-4 text-yellow-500" />
                      )}
                    </div>
                    {roleNames.length > 1 && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowDeleteModal(roleName);
                        }}
                        className="ml-2 text-gray-400 hover:text-red-500"
                        title="Delete role"
                        aria-label={`Delete ${getRoleDisplayName(roleName)}`}
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Active role configuration form */}
        {activeRole && form && (
          <div className="card">
            <div className="card-header">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    <CogIcon className="h-6 w-6 text-gray-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">
                      {getRoleDisplayName(activeRole)} Configuration
                    </h3>
                    <p className="text-sm text-gray-500">
                      Configure LLM provider and model for the {getRoleDisplayName(activeRole).toLowerCase()} role
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  {hasUnsavedChanges && (
                    <span className="badge badge-warning">Unsaved Changes</span>
                  )}
                  <span
                    className={clsx('badge', {
                      'badge-success': form.enabled && !hasUnsavedChanges,
                      'badge-error': !form.enabled,
                      'badge-info': hasUnsavedChanges,
                    })}
                  >
                    {form.enabled ? (hasUnsavedChanges ? 'Edited' : 'Active') : 'Inactive'}
                  </span>
                </div>
              </div>
            </div>

            <div className="card-body space-y-6">
              {/* Provider */}
              <ProviderSelector
                value={form.provider}
                onChange={onProviderChange}
                providers={providers}
                disabled={isSaving}
              />

              {/* Model */}
              <ModelSelector
                value={form.selected_model}
                onChange={onModelChange}
                provider={form.provider}
                models={getModelsForProvider(form.provider)}
                disabled={isSaving || !isProviderSupported(form.provider)}
              />

              {/* API key */}
              <ApiKeyInput
                value={form.api_key}
                onChange={onApiKeyChange}
                provider={form.provider}
                required={form.provider !== LLMProvider.OLLAMA}
                disabled={isSaving}
              />

              {/* Advanced settings */}
              <div className="border-t border-gray-200 pt-6">
                <h4 className="text-lg font-medium text-gray-900 mb-4">Advanced Settings</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Cost limit */}
                  <div>
                    <label htmlFor="cost_limit" className="block text-sm font-medium text-gray-700">
                      Monthly Cost Limit (USD)
                    </label>
                    <input
                      id="cost_limit"
                      type="number"
                      step="0.01"
                      className="form-input"
                      placeholder="100.00"
                      value={form.cost_limit ?? ''}
                      onChange={onNumericChange('cost_limit')}
                      disabled={isSaving}
                    />
                  </div>

                  {/* Max tokens */}
                  <div>
                    <label htmlFor="max_tokens" className="block text-sm font-medium text-gray-700">
                      Max Tokens
                    </label>
                    <input
                      id="max_tokens"
                      type="number"
                      className="form-input"
                      value={form.max_tokens}
                      onChange={onNumericChange('max_tokens')}
                      disabled={isSaving}
                    />
                  </div>

                  {/* Temperature */}
                  <div>
                    <label htmlFor="temperature" className="block text-sm font-medium text-gray-700">
                      Temperature
                    </label>
                    <input
                      id="temperature"
                      type="number"
                      step="0.1"
                      className="form-input"
                      value={form.temperature}
                      onChange={onNumericChange('temperature')}
                      disabled={isSaving}
                    />
                  </div>
                </div>
              </div>

              {/* Enabled */}
              <div className="flex items-center">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={form.enabled}
                    onChange={onEnabledToggle}
                    className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-offset-0 focus:ring-blue-200 focus:ring-opacity-50"
                    disabled={isSaving}
                  />
                  <span className="ml-2 text-sm text-gray-900">
                    Enable this role configuration
                  </span>
                </label>
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={handleReset}
                  disabled={!hasUnsavedChanges || isSaving}
                  className={clsx('btn-secondary', {
                    'opacity-50 cursor-not-allowed': !hasUnsavedChanges || isSaving,
                  })}
                >
                  Reset
                </button>
                <button
                  type="button"
                  onClick={handleSave}
                  disabled={isSaving || !form.selected_model || !form.provider}
                  className="btn-primary"
                >
                  {isSaving ? 'Saving...' : 'Save Changes'}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Create role modal */}
        {showCreateForm && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <h3 className="text-lg font-bold text-gray-900 mb-4">Create New Role</h3>

              <div className="space-y-4">
                <div>
                  <label htmlFor="role-name" className="block text-sm font-medium text-gray-700">
                    Role Name
                  </label>
                  <input
                    id="role-name"
                    type="text"
                    value={newRoleName}
                    onChange={(e) => setNewRoleName(e.target.value)}
                    className="form-input mt-1"
                    placeholder="e.g., Code Reviewer"
                  />
                </div>

                {createError && (
                  <div className="text-sm text-red-600">
                    {createError}
                  </div>
                )}
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setShowCreateForm(false);
                    setNewRoleName('');
                    setCreateError(null);
                  }}
                  className="btn-secondary"
                >
                  Cancel
                </button>
                <button
                  onClick={handleCreateRole}
                  disabled={!newRoleName.trim()}
                  className="btn-primary"
                >
                  Create Role
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Delete confirmation modal */}
        {showDeleteModal && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <h3 className="text-lg font-bold text-gray-900 mb-4">Delete Role</h3>
              <p className="text-sm text-gray-700">
                Are you sure you want to delete the role "{getRoleDisplayName(showDeleteModal)}"?
                This action cannot be undone.
              </p>

              {deleteError && (
                <div className="text-sm text-red-600 mt-3">{deleteError}</div>
              )}

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowDeleteModal(null)}
                  className="btn-secondary"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleDeleteRole(showDeleteModal)}
                  className="btn-danger"
                  disabled={roleNames.length <= 1}
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
};

// Extend the component props to receive injected auth props via HOC
export default withAuth(Home as React.FC<WithAuthInjectedProps>);