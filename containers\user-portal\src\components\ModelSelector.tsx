/**
 * ModelSelector Component
 * Dropdown that shows available models based on the selected provider
 */

import React, { useMemo } from 'react';
import Select from 'react-select';
import { CpuChipIcon, CurrencyDollarIcon } from '@heroicons/react/24/outline';
import clsx from 'clsx';
import { LL<PERSON>rovider } from '@/types/role';
import type { ModelSelectorProps, ModelOption } from '@/types/role';

// Mock data for models by provider
const MOCK_MODELS_BY_PROVIDER: Record<LLMProvider, ModelOption[]> = {
  [LLMProvider.OLLAMA]: [
    {
      value: 'llama2:7b',
      label: 'Llama 2 7B',
      description: 'Efficient 7B parameter model for general tasks',
      context_length: 4096,
    },
    {
      value: 'llama2:13b',
      label: 'Llama 2 13B',
      description: 'Balanced 13B parameter model with good performance',
      context_length: 4096,
    },
    {
      value: 'codellama:7b',
      label: 'CodeLlama 7B',
      description: 'Specialized code generation model',
      context_length: 16384,
    },
    {
      value: 'codellama:13b',
      label: 'CodeLlama 13B',
      description: 'Larger code generation model with better accuracy',
      context_length: 16384,
    },
    {
      value: 'deepseek-coder:6.7b',
      label: 'DeepSeek Coder 6.7B',
      description: 'Advanced code understanding and generation',
      context_length: 16384,
    },
  ],
  [LLMProvider.OPENROUTER]: [
    {
      value: 'anthropic/claude-3-sonnet',
      label: 'Claude 3 Sonnet',
      description: 'Balanced performance and cost for most tasks',
      cost_per_token: 0.000003,
      context_length: 200000,
    },
    {
      value: 'anthropic/claude-3-haiku',
      label: 'Claude 3 Haiku',
      description: 'Fast and cost-effective for simple tasks',
      cost_per_token: 0.00000025,
      context_length: 200000,
    },
    {
      value: 'openai/gpt-4',
      label: 'GPT-4',
      description: 'Most capable OpenAI model',
      cost_per_token: 0.00003,
      context_length: 8192,
    },
    {
      value: 'openai/gpt-4-turbo',
      label: 'GPT-4 Turbo',
      description: 'Faster GPT-4 with longer context',
      cost_per_token: 0.00001,
      context_length: 128000,
    },
    {
      value: 'openai/gpt-3.5-turbo',
      label: 'GPT-3.5 Turbo',
      description: 'Fast and cost-effective for most tasks',
      cost_per_token: 0.0000005,
      context_length: 16385,
    },
  ],
  [LLMProvider.OPENAI]: [
    {
      value: 'gpt-4',
      label: 'GPT-4',
      description: 'Most capable OpenAI model',
      cost_per_token: 0.00003,
      context_length: 8192,
    },
    {
      value: 'gpt-4-turbo',
      label: 'GPT-4 Turbo',
      description: 'Faster GPT-4 with longer context',
      cost_per_token: 0.00001,
      context_length: 128000,
    },
    {
      value: 'gpt-4o',
      label: 'GPT-4o',
      description: 'Optimized GPT-4 for better performance',
      cost_per_token: 0.000005,
      context_length: 128000,
    },
    {
      value: 'gpt-3.5-turbo',
      label: 'GPT-3.5 Turbo',
      description: 'Fast and cost-effective',
      cost_per_token: 0.0000005,
      context_length: 16385,
    },
  ],
  [LLMProvider.ANTHROPIC]: [
    {
      value: 'claude-3-opus',
      label: 'Claude 3 Opus',
      description: 'Most capable Claude model for complex tasks',
      cost_per_token: 0.000015,
      context_length: 200000,
    },
    {
      value: 'claude-3-sonnet',
      label: 'Claude 3 Sonnet',
      description: 'Balanced performance and cost',
      cost_per_token: 0.000003,
      context_length: 200000,
    },
    {
      value: 'claude-3-haiku',
      label: 'Claude 3 Haiku',
      description: 'Fast and cost-effective',
      cost_per_token: 0.00000025,
      context_length: 200000,
    },
  ],
};

const ModelSelector: React.FC<ModelSelectorProps> = ({
  value,
  onChange,
  models,
  provider,
  disabled = false,
  error,
}) => {
  // Use provided models or fallback to mock data
  const availableModels = useMemo(() => {
    if (models && models.length > 0) {
      return models;
    }
    return MOCK_MODELS_BY_PROVIDER[provider] || [];
  }, [models, provider]);

  const selectedModel = availableModels.find(m => m.value === value);

  const customStyles = {
    control: (provided: any, state: any) => ({
      ...provided,
      minHeight: '42px',
      borderColor: error
        ? '#ef4444'
        : state.isFocused
        ? '#3b82f6'
        : '#d1d5db',
      borderWidth: '1px',
      borderRadius: '8px',
      boxShadow: state.isFocused
        ? error
          ? '0 0 0 3px rgba(239, 68, 68, 0.1)'
          : '0 0 0 3px rgba(59, 130, 246, 0.1)'
        : 'none',
      '&:hover': {
        borderColor: error ? '#ef4444' : '#9ca3af',
      },
    }),
    option: (provided: any, state: any) => ({
      ...provided,
      backgroundColor: state.isSelected
        ? '#3b82f6'
        : state.isFocused
        ? '#f3f4f6'
        : 'white',
      color: state.isSelected ? 'white' : '#374151',
      padding: '12px 16px',
      '&:hover': {
        backgroundColor: state.isSelected ? '#3b82f6' : '#f3f4f6',
      },
    }),
    placeholder: (provided: any) => ({
      ...provided,
      color: '#9ca3af',
    }),
    singleValue: (provided: any) => ({
      ...provided,
      color: '#374151',
    }),
  };

  const formatOptionLabel = (option: ModelOption) => (
    <div className="py-1">
      <div className="flex items-center justify-between">
        <div className="font-medium text-gray-900">{option.label}</div>
        <div className="flex items-center space-x-2">
          {option.cost_per_token && (
            <span className="inline-flex items-center rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800">
              <CurrencyDollarIcon className="h-3 w-3 mr-1" />
              ${option.cost_per_token.toFixed(6)}/token
            </span>
          )}
          {option.context_length && (
            <span className="inline-flex items-center rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800">
              <CpuChipIcon className="h-3 w-3 mr-1" />
              {option.context_length.toLocaleString()} ctx
            </span>
          )}
        </div>
      </div>
      {option.description && (
        <div className="text-sm text-gray-500 mt-1">{option.description}</div>
      )}
    </div>
  );

  const formatSingleValue = (option: ModelOption) => (
    <div className="flex items-center">
      <span className="font-medium">{option.label}</span>
      {option.cost_per_token && (
        <span className="ml-2 text-xs text-gray-500">
          ${option.cost_per_token.toFixed(6)}/token
        </span>
      )}
    </div>
  );

  if (availableModels.length === 0) {
    return (
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          Model Selection
          <span className="text-red-500 ml-1">*</span>
        </label>
        <div className="flex items-center justify-center h-42 bg-gray-50 rounded-lg border border-gray-200">
          <div className="text-center text-gray-500">
            <CpuChipIcon className="h-8 w-8 mx-auto mb-2" />
            <p>No models available for {provider}</p>
            <p className="text-sm">Select a provider to see available models</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <label
        htmlFor="model-select"
        className="block text-sm font-medium text-gray-700"
      >
        Model Selection
        <span className="text-red-500 ml-1">*</span>
      </label>

      <Select
        instanceId="model-select"
        inputId="model-select"
        value={selectedModel}
        onChange={(option) => onChange(option?.value || '')}
        options={availableModels}
        formatOptionLabel={formatOptionLabel}
        getOptionValue={(option) => option.value}
        styles={customStyles}
        isDisabled={disabled}
        placeholder="Select a model..."
        className={clsx('react-select-container', {
          'opacity-50 cursor-not-allowed': disabled,
        })}
        classNamePrefix="react-select"
        aria-label="Select Model"
        aria-describedby={error ? 'model-error' : undefined}
        noOptionsMessage={() => `No models available for ${provider}`}
      />

      {error && (
        <p
          id="model-error"
          className="text-sm text-red-600"
          role="alert"
          aria-live="polite"
        >
          {error}
        </p>
      )}

      {selectedModel && !error && (
        <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
          <div className="font-medium mb-2">Model Details:</div>
          <div className="grid grid-cols-1 gap-2">
            <div><strong>Name:</strong> {selectedModel.label}</div>
            {selectedModel.description && (
              <div><strong>Description:</strong> {selectedModel.description}</div>
            )}
            {selectedModel.context_length && (
              <div><strong>Context Length:</strong> {selectedModel.context_length.toLocaleString()} tokens</div>
            )}
            {selectedModel.cost_per_token && (
              <div><strong>Cost:</strong> ${selectedModel.cost_per_token.toFixed(6)} per token</div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ModelSelector;